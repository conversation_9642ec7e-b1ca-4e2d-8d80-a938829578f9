package tool

import (
	"context"
	"fmt"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/types"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/utils"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

// ToolListInputData 查询工具列表输入数据
type ToolListInputData struct {
	SessionID   int64  `json:"session_id,omitempty"`
	SessionCode string `json:"session_code,omitempty"`
}

// ToolListOutputData 查询工具列表输出数据
type ToolListOutputData struct {
	MCPTools []types.MCPTool `json:"mcp_tools"`
}

// ToolList 查询工具列表服务
type ToolList struct {
	base.Service[ToolListInputData, ToolListOutputData]
	common *ToolCallCommon // 通用逻辑处理器
}

// NewToolList 创建查询工具列表服务实例
func NewToolList() *ToolList {
	return &ToolList{
		common: &ToolCallCommon{},
	}
}

// Execute 执行查询工具列表逻辑
func (s *ToolList) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData
	if input.SessionID == 0 && input.SessionCode == "" {
		return nil, nil, &lib_error.CustomErr{Code: errcode.UserInputError, Msg: "session_id 和 session_code 必须提供一个"}
	}

	var session *dao_session.ObjSession
	if input.SessionCode != "" {
		session, err = dao_session.SessionBusinessIns.GetSessionByCode(ctx, input.SessionCode)
	} else {
		session, err = dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, input.SessionID)
	}

	if err != nil {
		return nil, nil, err
	}

	if session.ContainerStatus == dao_session.ContainerStatusPending {
		return nil, nil, &lib_error.CustomErr{Code: errcode.K8sContainerCreateError,
			Msg: "容器正在初始化，请稍后"}
	}
	if session.McpTools == nil {
		return nil, nil, &lib_error.CustomErr{Code: errcode.K8sContainerCreateError,
			Msg: "工具列表未初始化，请稍后"}
	}
	// 将JSONData转换为MCPTool列表
	mcpTools, err := utils.ConvertJSONDataToMCPTools(&session.McpTools)
	if err != nil {
		return &lib_error.CustomErr{Code: errcode.SysFileReadError, Msg: fmt.Sprintf("解析MCP工具列表失败: %v", err)}, nil, err
	}

	return &ToolListOutputData{MCPTools: mcpTools}, nil, nil
}

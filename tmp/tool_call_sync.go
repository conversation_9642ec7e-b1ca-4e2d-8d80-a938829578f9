package tool

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	"icode.baidu.com/baidu/gdp/ghttp"

	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

// ToolCallSyncInputData 同步工具调用输入数据
type ToolCallSyncInputData struct {
	ToolCallRequest
}

// ToolCallSyncOutputData 同步工具调用输出数据
type ToolCallSyncOutputData struct {
	ToolCallResponse
}

// ToolCallCommon 通用工具调用逻辑（简化版本）
type ToolCallCommon struct{}

// ValidateToolCallRequest 验证工具调用请求参数
func (tc *ToolCallCommon) ValidateToolCallRequest(ctx context.Context, req *ToolCallRequest) error {
	// 简化的验证逻辑
	if req.Name == "" {
		return fmt.Errorf("tool name不能为空")
	}
	return nil
}

// ValidateSession 验证Session有效性
func (tc *ToolCallCommon) ValidateSession(ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
	// 简化的session验证逻辑
	if sessionCode != "" {
		return dao_session.SessionBusinessIns.GetSessionByCode(ctx, sessionCode)
	} else if sessionID > 0 {
		return dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, sessionID)
	}
	return nil, fmt.Errorf("session_id 或 session_code 必须提供一个")
}

// CreateToolCallTask 创建工具调用任务记录
func (tc *ToolCallCommon) CreateToolCallTask(ctx context.Context, req *ToolCallRequest, session *dao_session.ObjSession) (*dao_tool_call_task.ObjToolCallTask, error) {
	// 简化的任务创建逻辑
	task := &dao_tool_call_task.ObjToolCallTask{
		CallID:    req.CallID,
		SessionID: session.SessionID,
		ToolName:  req.Name,
		Arguments: req.Arguments,
		Status:    dao_tool_call_task.ToolCallTaskStatusPending,
	}
	taskID, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
	if err != nil {
		return nil, err
	}
	task.TaskID = taskID
	return task, nil
}

// WaitForTaskCompletion 等待任务完成（简化版本）
func (tc *ToolCallCommon) WaitForTaskCompletion(ctx context.Context, sessionID int64, callID string, timeoutSecs int) (*ToolCallResponse, error) {
	// 简化的等待逻辑
	return &ToolCallResponse{
		CallID: callID,
		Status: "success",
		Result: "{}",
	}, nil
}

// ToolCallSync 同步工具调用服务
type ToolCallSync struct {
	base.Service[ToolCallSyncInputData, ToolCallSyncOutputData]
	common *ToolCallCommon // 通用逻辑处理器
}

// NewToolCallSync 创建同步工具调用服务实例
func NewToolCallSync() *ToolCallSync {
	return &ToolCallSync{
		common: &ToolCallCommon{},
	}
}

// Execute 执行同步工具调用逻辑
func (s *ToolCallSync) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	// 从请求中解析输入数据
	var input ToolCallSyncInputData
	if err := json.Unmarshal([]byte(req.Body), &input); err != nil {
		return nil, nil, fmt.Errorf("解析请求参数失败: %w", err)
	}

	// 2. 验证请求参数
	if err := s.common.ValidateToolCallRequest(ctx, &input.ToolCallRequest); err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("工具调用参数验证失败: %v", err))
		return nil, nil, err
	}

	// 3. 验证Session有效性
	session, err := s.common.ValidateSession(ctx, input.ToolCallRequest.SessionID, input.ToolCallRequest.SessionCode)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("Session验证失败: %v", err))
		return nil, nil, err
	}

	// 4. 根据session是否有image_id判断执行路径
	if session.ImageID != "" {
		// 有image_id，走k8s_proxy_client直接执行路径
		response, err := s.executeToolViaK8sProxy(ctx, session, &input.ToolCallRequest)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("k8s_proxy执行工具失败: %v", err))
			return nil, nil, err
		}

		// 构建返回结果
		output := &ToolCallSyncOutputData{
			ToolCallResponse: *response,
		}
		return output, nil, nil
	} else {
		// 没有image_id，走原有的MCP工具调用路径
		resource.LoggerService.Notice(ctx, fmt.Sprintf("使用MCP工具调用路径: session_id=%d", session.SessionID))

		// 校验call_id不重复
		existingTask, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, session.SessionID, input.ToolCallRequest.CallID)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("查询已存在任务失败: %v", err))
			return nil, nil, err
		}
		if existingTask != nil {
			return nil, nil, &lib_error.CustomErr{Code: errcode.ToolCallIDDuplicate, Msg: "call_id已存在"}
		}

		// 创建工具调用任务记录
		_, err = s.common.CreateToolCallTask(ctx, &input.ToolCallRequest, session)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("创建工具调用任务失败: %v", err))
			return nil, nil, err
		}

		// 等待任务完成（同步等待）
		response, err := s.common.WaitForTaskCompletion(ctx, session.SessionID, input.ToolCallRequest.CallID, input.ToolCallRequest.TimeoutSecs)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("等待任务完成失败: %v", err))
			return nil, nil, err
		}

		// 构建返回结果
		output := &ToolCallSyncOutputData{
			ToolCallResponse: *response,
		}
		return output, nil, nil
	}
}

// getStrategyForSession 根据session选择策略
func (s *ToolCallSync) getStrategyForSession(session *dao_session.ObjSession) ToolCallStrategy {
	// 检查是否是sweagent策略
	if strings.HasPrefix(strings.ToLower(session.SessionCode), "sweagent") ||
		   (session.ImageID != "" && strings.Contains(strings.ToLower(session.ImageID), "sweagent")) {
		return &SweAgentStrategy{}
	}
	// 默认返回MCP策略
	return &MCPStrategy{}
}

// executeToolViaK8sProxy 通过k8s_proxy_client直接执行工具调用
func (s *ToolCallSync) executeToolViaK8sProxy(ctx context.Context, session *dao_session.ObjSession, req *ToolCallRequest) (*ToolCallResponse, error) {

	// 1. 检查call_id是否重复（在tool_call_task表中）
	existingTask, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, session.SessionID, req.CallID)
	if err != nil {
		return nil, fmt.Errorf("查询已存在工具调用任务失败: %w", err)
	}
	if existingTask != nil {
		return nil, &lib_error.CustomErr{Code: errcode.ToolCallIDDuplicate, Msg: "call_id已存在"}
	}

	// 2. 根据session自动选择策略
	strategy := s.getStrategyForSession(session)
	if strategy == nil {
		return nil, fmt.Errorf("无法找到适合的工具调用策略")
	}

	resource.LoggerService.Notice(ctx, fmt.Sprintf("使用策略: %s, session_id=%d, session_code=%s",
		strategy.GetStrategyName(), session.SessionID, session.SessionCode))

	// 3. 解析参数并构建执行命令
	var arguments map[string]interface{}
	if req.Arguments != "" && req.Arguments != "{}" {
		if err := json.Unmarshal([]byte(req.Arguments), &arguments); err != nil {
			return nil, fmt.Errorf("解析工具参数失败: %w", err)
		}
	}

	command, err := strategy.BuildCommand(req.Name, arguments)
	if err != nil {
		return nil, fmt.Errorf("构建执行命令失败: %w", err)
	}

	// 4. 创建工具调用任务记录，状态直接设为running
	now := time.Now()
	task := &dao_tool_call_task.ObjToolCallTask{
		CallID:    req.CallID,
		SessionID: session.SessionID,
		ToolName:  req.Name,
		Arguments: req.Arguments,
		Status:    dao_tool_call_task.ToolCallTaskStatusRunning,
		StartedAt: &now,
	}

	taskID, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
	if err != nil {
		return nil, err
	}

	// 5. 调用k8s_proxy_client执行命令
	execReq := &rpc_k8s_proxy.ExecCommandRequest{
		SourceType: "mcp",
		JobName:    session.JobID,
		Command:    command,
	}

	execResp, err := rpc_k8s_proxy.K8sProxyClientIns.ExecCommand(ctx, execReq)
	if err != nil {
		// 更新状态为失败
		errorMsg := fmt.Sprintf("k8s执行失败: %v", err)
		_ = dao_tool_call_task.ToolCallTaskBusinessIns.UpdateStatus(ctx, taskID, dao_tool_call_task.ToolCallTaskStatusFailed, errorMsg)
		return nil, fmt.Errorf("k8s执行命令失败: %w", err)
	}

	// 6. 构建工具执行结果并使用策略格式化输出
	toolResult := &ToolExecutionResult{
		Stdout:   execResp.Stdout,
		Stderr:   execResp.Stderr,
		ExitCode: execResp.Code,
		Command:  fmt.Sprintf("%v", command),
		ToolName: req.Name,
	}

	formattedOutput := strategy.FormatOutput(req.Name, toolResult)

	// 7. 构建最终结果JSON（保持与R2E-Gym一致的简单格式）
	var resultJSON []byte
	var status string
	var errorMessage string

	if execResp.Code == 0 {
		status = "success"
		result := map[string]interface{}{
			"content": formattedOutput,
		}
		resultJSON, _ = json.Marshal(result)
	} else {
		// 即使有错误码，也按照R2E-Gym的逻辑处理（包含错误信息但不一定是失败状态）
		status = "success" // R2E-Gym中工具执行完成就是成功，错误信息包含在输出中
		result := map[string]interface{}{
			"content": formattedOutput,
		}
		resultJSON, _ = json.Marshal(result)

		// 记录错误信息用于日志，但不影响返回状态
		if execResp.Message != "" {
			errorMessage = execResp.Message
		}
	}

	// 6. 更新任务状态为成功并保存结果
	completedAt := time.Now()
	task.Status = dao_tool_call_task.ToolCallTaskStatusSuccess
	task.Result = string(resultJSON)
	task.CompletedAt = &completedAt

	err = dao_tool_call_task.ToolCallTaskBusinessIns.Update(ctx, task)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("更新工具调用任务结果失败: %v", err))
	}

	resource.LoggerService.Notice(ctx, fmt.Sprintf("k8s_proxy执行工具完成: session_id=%d, call_id=%s, stdout_length=%d, stderr_length=%d", session.SessionID, req.CallID, len(execResp.Stdout), len(execResp.Stderr)))

	// 7. 构建返回结果
	return &ToolCallResponse{
		CallID:       req.CallID,
		Status:       "success",
		Result:       string(resultJSON),
		ErrorMessage: "",
		OldEnvMD5:    "",
		NewEnvMD5:    "",
		OldEnvURL:    "",
		NewEnvURL:    "",
	}, nil
}

package tool

import (
	"encoding/json"
	"fmt"
)

// CommandBuildStrategy 命令构建策略接口
type CommandBuildStrategy interface {
	// BuildCommand 根据工具名称和参数构建执行命令
	BuildCommand(toolName string, arguments map[string]interface{}) ([]string, error)
	// GetStrategyName 获取策略名称
	GetStrategyName() string
}

// CommandStrategyManager 命令策略管理器
type CommandStrategyManager struct {
	strategies map[string]CommandBuildStrategy
}

// NewCommandStrategyManager 创建命令策略管理器
func NewCommandStrategyManager() *CommandStrategyManager {
	manager := &CommandStrategyManager{
		strategies: make(map[string]CommandBuildStrategy),
	}

	// 注册默认策略
	manager.RegisterStrategy(&SweAgentStrategy{})

	return manager
}

// RegisterStrategy 注册策略
func (m *CommandStrategyManager) RegisterStrategy(strategy CommandBuildStrategy) {
	m.strategies[strategy.GetStrategyName()] = strategy
}

// GetStrategy 获取策略
func (m *CommandStrategyManager) GetStrategy(strategyName string) (CommandBuildStrategy, error) {
	strategy, exists := m.strategies[strategyName]
	if !exists {
		return nil, fmt.Errorf("strategy '%s' not found", strategyName)
	}
	return strategy, nil
}

// BuildCommand 使用指定策略构建命令
func (m *CommandStrategyManager) BuildCommand(strategyName, toolName string, argumentsJSON string) ([]string, error) {
	strategy, err := m.GetStrategy(strategyName)
	if err != nil {
		return nil, err
	}

	// 解析参数
	var arguments map[string]interface{}
	if argumentsJSON != "" && argumentsJSON != "{}" {
		if err := json.Unmarshal([]byte(argumentsJSON), &arguments); err != nil {
			return nil, fmt.Errorf("failed to parse arguments: %w", err)
		}
	}

	return strategy.BuildCommand(toolName, arguments)
}

// SweAgentStrategy SweAgent策略实现
type SweAgentStrategy struct{}

// GetStrategyName 获取策略名称
func (s *SweAgentStrategy) GetStrategyName() string {
	return "sweagent"
}

// BuildCommand 构建命令
// 输入示例: tool_name="file_editor", arguments={"command":"view","path":"/testbed","concise":true}
// 输出示例: ["file_editor", "view", "--path", "/testbed", "--concise", "True"]
func (s *SweAgentStrategy) BuildCommand(toolName string, arguments map[string]interface{}) ([]string, error) {
	command := []string{toolName}

	// 处理command参数（作为子命令）
	if cmd, ok := arguments["command"]; ok {
		command = append(command, fmt.Sprintf("%v", cmd))
	}

	// 为了保证参数顺序的一致性，我们按照特定顺序处理参数
	// 先处理常见的参数，然后处理其他参数
	paramOrder := []string{"path", "concise", "verbose", "count", "input", "output"}

	// 按顺序处理已知参数
	for _, key := range paramOrder {
		if value, ok := arguments[key]; ok && key != "command" {
			command = append(command, fmt.Sprintf("--%s", key))

			// 添加参数值，特殊处理布尔值
			switch v := value.(type) {
			case bool:
				if v {
					command = append(command, "True")
				} else {
					command = append(command, "False")
				}
			default:
				command = append(command, fmt.Sprintf("%v", value))
			}
		}
	}

	// 处理其他未在paramOrder中的参数
	for key, value := range arguments {
		if key == "command" {
			continue // 已经处理过了
		}

		// 检查是否已经在paramOrder中处理过
		found := false
		for _, orderedKey := range paramOrder {
			if key == orderedKey {
				found = true
				break
			}
		}
		if found {
			continue // 已经处理过了
		}

		// 添加参数名
		command = append(command, fmt.Sprintf("--%s", key))

		// 添加参数值，特殊处理布尔值
		switch v := value.(type) {
		case bool:
			if v {
				command = append(command, "True")
			} else {
				command = append(command, "False")
			}
		default:
			command = append(command, fmt.Sprintf("%v", value))
		}
	}

	return command, nil
}

// 全局策略管理器实例
var GlobalCommandStrategyManager = NewCommandStrategyManager()
